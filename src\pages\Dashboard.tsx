
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { ProfileHeader } from '@/components/ProfileHeader';
import { ProfileSections } from '@/components/ProfileSections';
import { DatingPlans } from '@/components/DatingPlans';
import { FeedbackSystem } from '@/components/FeedbackSystem';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MessagingSystem } from '@/components/MessagingSystem';
import { VisibilitySettings } from '@/components/VisibilitySettings';
import { DatingPlanFeedback } from '@/components/DatingPlanFeedback';

const Dashboard = () => {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const navigate = useNavigate();
  const [sections, setSections] = useState([]);
  const [datingPlans, setDatingPlans] = useState([]);
  const [feedbacks, setFeedbacks] = useState([]);
  
  // New state for additional features
  const [conversations, setConversations] = useState([]);
  const [messages, setMessages] = useState([]);
  const [visibilitySettings, setVisibilitySettings] = useState({
    isProfileVisible: true,
    hideFromPublic: false,
    relationshipStatus: 'single' as 'single' | 'dating' | 'married',
    partnerEmail: '',
    hideFromEveryone: false,
  });
  const [datePlanFeedbacks, setDatePlanFeedbacks] = useState([]);
  const [dateRequests, setDateRequests] = useState([]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, isLoading, navigate]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ProfileHeader user={user} onLogout={logout} />
      
      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="profile" className="mt-8">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="dating-plans">Dating Plans</TabsTrigger>
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
            <TabsTrigger value="messages">Messages</TabsTrigger>
            <TabsTrigger value="visibility">Visibility</TabsTrigger>
            <TabsTrigger value="date-feedback">Date Feedback</TabsTrigger>
            {user?.isAdmin && <TabsTrigger value="admin">Admin</TabsTrigger>}
          </TabsList>
          
          <TabsContent value="profile" className="mt-6">
            <ProfileSections 
              sections={sections} 
              onSectionsChange={setSections}
            />
          </TabsContent>
          
          <TabsContent value="dating-plans" className="mt-6">
            <DatingPlans 
              plans={datingPlans} 
              onPlansChange={setDatingPlans}
            />
          </TabsContent>
          
          <TabsContent value="feedback" className="mt-6">
            <FeedbackSystem 
              feedbacks={feedbacks} 
              onFeedbacksChange={setFeedbacks}
            />
          </TabsContent>
          
          <TabsContent value="messages" className="mt-6">
            <MessagingSystem
              conversations={conversations}
              messages={messages}
              onConversationsChange={setConversations}
              onMessagesChange={setMessages}
            />
          </TabsContent>
          
          <TabsContent value="visibility" className="mt-6">
            <VisibilitySettings
              settings={visibilitySettings}
              onSettingsChange={setVisibilitySettings}
            />
          </TabsContent>
          
          <TabsContent value="date-feedback" className="mt-6">
            <DatingPlanFeedback
              feedbacks={datePlanFeedbacks}
              requests={dateRequests}
              onFeedbacksChange={setDatePlanFeedbacks}
              onRequestsChange={setDateRequests}
            />
          </TabsContent>
          
          {user?.isAdmin && (
            <TabsContent value="admin" className="mt-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-2xl font-bold mb-4">Admin Dashboard</h2>
                <p className="text-gray-600">Welcome, Administrator! Admin features coming soon.</p>
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
