
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Plus } from 'lucide-react';

interface Section {
  id: string;
  type: 'PREDEFINED' | 'CUSTOM';
  name: string;
  contentType: 'TEXT_AREA' | 'LIST_OF_ITEMS' | 'COMPLEX_OBJECT_LIST' | 'SOCIAL_MEDIA_LINKS';
  isLocked: boolean;
  password?: string;
  content: any[];
}

interface SectionContentProps {
  section: Section;
}

export const SectionContent = ({ section }: SectionContentProps) => {
  if (section.contentType === 'TEXT_AREA') {
    return (
      <Textarea
        placeholder={`Enter your ${section.name.toLowerCase()}...`}
        rows={4}
      />
    );
  }

  if (section.contentType === 'LIST_OF_ITEMS') {
    return (
      <div className="space-y-2">
        <Input placeholder={`Add ${section.name.toLowerCase()}...`} />
        <Button size="sm" variant="outline">
          <Plus className="w-4 h-4 mr-2" />
          Add Item
        </Button>
      </div>
    );
  }

  if (section.contentType === 'COMPLEX_OBJECT_LIST') {
    return (
      <div className="space-y-4">
        {section.name === 'Education' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input placeholder="Institution" />
            <Input placeholder="Degree" />
            <Input placeholder="Graduation Year" />
          </div>
        )}
        {section.name === 'Work Experience' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input placeholder="Company" />
            <Input placeholder="Role" />
            <Input placeholder="Start Date" />
            <Input placeholder="End Date" />
          </div>
        )}
        <Button size="sm" variant="outline">
          <Plus className="w-4 h-4 mr-2" />
          Add Entry
        </Button>
      </div>
    );
  }

  if (section.contentType === 'SOCIAL_MEDIA_LINKS') {
    return (
      <div className="space-y-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input placeholder="Platform (e.g., LinkedIn)" />
          <Input placeholder="URL" />
        </div>
        <Button size="sm" variant="outline">
          <Plus className="w-4 h-4 mr-2" />
          Add Link
        </Button>
      </div>
    );
  }

  return null;
};
