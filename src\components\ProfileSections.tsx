import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { AddPredefinedSections } from './AddPredefinedSections';
import { AddCustomSection } from './AddCustomSection';
import { SectionCard } from './SectionCard';

interface Section {
  id: string;
  type: 'PREDEFINED' | 'CUSTOM';
  name: string;
  contentType: 'TEXT_AREA' | 'LIST_OF_ITEMS' | 'COMPLEX_OBJECT_LIST' | 'SOCIAL_MEDIA_LINKS';
  isLocked: boolean;
  password?: string;
  content: any[];
}

interface ProfileSectionsProps {
  sections: Section[];
  onSectionsChange: (sections: Section[]) => void;
}

export const ProfileSections = ({ sections, onSectionsChange }: ProfileSectionsProps) => {
  const { toast } = useToast();

  const toggleSectionLock = (sectionId: string) => {
    const updatedSections = sections.map(section => {
      if (section.id === sectionId) {
        return { ...section, isLocked: !section.isLocked };
      }
      return section;
    });
    onSectionsChange(updatedSections);
  };

  const deleteSection = (sectionId: string) => {
    const updatedSections = sections.filter(section => section.id !== sectionId);
    onSectionsChange(updatedSections);
    toast({
      title: "Section deleted",
      description: "The section has been removed from your profile.",
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Profile Sections</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <AddPredefinedSections 
            sections={sections} 
            onSectionsChange={onSectionsChange}
          />
          <AddCustomSection 
            sections={sections} 
            onSectionsChange={onSectionsChange}
          />
        </CardContent>
      </Card>

      {/* Existing Sections */}
      {sections.map((section) => (
        <SectionCard
          key={section.id}
          section={section}
          onToggleLock={toggleSectionLock}
          onDelete={deleteSection}
        />
      ))}

      {sections.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-500">No sections added yet. Start by adding some standard sections above!</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
