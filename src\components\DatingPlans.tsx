import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Calendar, Bell, Heart, Plus, Trash2 } from 'lucide-react';

interface DatingPlan {
  id: string;
  title: string;
  description: string;
  targetDate: string;
  milestoneType: 'DATE' | 'MARRIAGE' | 'CUSTOM';
  notifications: {
    id: string;
    message: string;
    daysBeforeTarget: number;
    isCompleted: boolean;
  }[];
  isActive: boolean;
}

interface DatingPlansProps {
  plans: DatingPlan[];
  onPlansChange: (plans: DatingPlan[]) => void;
}

export const DatingPlans = ({ plans, onPlansChange }: DatingPlansProps) => {
  const [newPlan, setNewPlan] = useState({
    title: '',
    description: '',
    targetDate: '',
    milestoneType: 'DATE' as const,
  });
  const [showAddForm, setShowAddForm] = useState(false);
  const { toast } = useToast();

  const addPlan = () => {
    if (!newPlan.title || !newPlan.targetDate) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    const plan: DatingPlan = {
      id: Date.now().toString(),
      ...newPlan,
      notifications: [
        {
          id: Date.now().toString() + '1',
          message: `Reminder: ${newPlan.title} is coming up!`,
          daysBeforeTarget: 7,
          isCompleted: false,
        }
      ],
      isActive: true,
    };

    onPlansChange([...plans, plan]);
    setNewPlan({
      title: '',
      description: '',
      targetDate: '',
      milestoneType: 'DATE',
    });
    setShowAddForm(false);
    
    toast({
      title: "Dating plan created!",
      description: `${newPlan.title} has been added to your plans.`,
    });
  };

  const deletePlan = (planId: string) => {
    const updatedPlans = plans.filter(plan => plan.id !== planId);
    onPlansChange(updatedPlans);
    toast({
      title: "Plan deleted",
      description: "The dating plan has been removed.",
    });
  };

  const togglePlanStatus = (planId: string) => {
    const updatedPlans = plans.map(plan => 
      plan.id === planId ? { ...plan, isActive: !plan.isActive } : plan
    );
    onPlansChange(updatedPlans);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Interactive Dating Plans
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!showAddForm ? (
            <Button onClick={() => setShowAddForm(true)} className="gap-2">
              <Plus className="w-4 h-4" />
              Add Dating Plan
            </Button>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="plan-title">Plan Title *</Label>
                <Input
                  id="plan-title"
                  placeholder="e.g., First Date, Anniversary, Wedding"
                  value={newPlan.title}
                  onChange={(e) => setNewPlan({ ...newPlan, title: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="plan-description">Description</Label>
                <Textarea
                  id="plan-description"
                  placeholder="Describe your dating milestone..."
                  value={newPlan.description}
                  onChange={(e) => setNewPlan({ ...newPlan, description: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="target-date">Target Date *</Label>
                <Input
                  id="target-date"
                  type="date"
                  value={newPlan.targetDate}
                  onChange={(e) => setNewPlan({ ...newPlan, targetDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="milestone-type">Milestone Type</Label>
                <select
                  id="milestone-type"
                  className="w-full p-2 border rounded-md"
                  value={newPlan.milestoneType}
                  onChange={(e) => setNewPlan({ ...newPlan, milestoneType: e.target.value as any })}
                >
                  <option value="DATE">Dating Milestone</option>
                  <option value="MARRIAGE">Marriage Milestone</option>
                  <option value="CUSTOM">Custom Milestone</option>
                </select>
              </div>
              <div className="flex gap-2">
                <Button onClick={addPlan}>Create Plan</Button>
                <Button variant="outline" onClick={() => setShowAddForm(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Existing Plans */}
      {plans.map((plan) => (
        <Card key={plan.id} className={plan.isActive ? '' : 'opacity-60'}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                {plan.title}
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant={plan.isActive ? "destructive" : "default"}
                  onClick={() => togglePlanStatus(plan.id)}
                >
                  {plan.isActive ? 'Pause' : 'Activate'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => deletePlan(plan.id)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-gray-600">{plan.description}</p>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4" />
                Target Date: {new Date(plan.targetDate).toLocaleDateString()}
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <Bell className="h-4 w-4" />
                  Notifications:
                </div>
                {plan.notifications.map((notification) => (
                  <div key={notification.id} className="ml-6 p-2 bg-gray-50 rounded text-sm">
                    {notification.message} ({notification.daysBeforeTarget} days before)
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {plans.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-500">No dating plans created yet. Add your first plan above!</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
