
import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: string;
  email: string;
  name?: string;
  isAdmin?: boolean;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored authentication
    const storedUser = localStorage.getItem('user');
    const token = localStorage.getItem('access_token');
    
    if (storedUser && token) {
      setUser(JSON.parse(storedUser));
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      console.log('Login attempt:', { email, password });
      
      // Check for admin credentials
      if (email === '<EMAIL>' && password === 'password123!') {
        const adminUser = { id: 'admin', email, name: 'Administrator', isAdmin: true };
        const mockToken = 'admin-jwt-token';
        
        localStorage.setItem('user', JSON.stringify(adminUser));
        localStorage.setItem('access_token', mockToken);
        setUser(adminUser);
        return;
      }
      
      // Regular user login
      const mockUser = { id: '1', email, name: 'John Doe' };
      const mockToken = 'mock-jwt-token';
      
      localStorage.setItem('user', JSON.stringify(mockUser));
      localStorage.setItem('access_token', mockToken);
      setUser(mockUser);
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, name: string) => {
    setIsLoading(true);
    try {
      console.log('Register attempt:', { email, password, name });
      const mockUser = { id: '1', email, name };
      const mockToken = 'mock-jwt-token';
      
      localStorage.setItem('user', JSON.stringify(mockUser));
      localStorage.setItem('access_token', mockToken);
      setUser(mockUser);
    } catch (error) {
      console.error('Register error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('access_token');
    setUser(null);
  };

  const value = {
    user,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    isLoading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
