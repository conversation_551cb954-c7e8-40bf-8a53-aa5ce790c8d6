
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Plus } from 'lucide-react';

interface Section {
  id: string;
  type: 'PREDEFINED' | 'CUSTOM';
  name: string;
  contentType: 'TEXT_AREA' | 'LIST_OF_ITEMS' | 'COMPLEX_OBJECT_LIST' | 'SOCIAL_MEDIA_LINKS';
  isLocked: boolean;
  password?: string;
  content: any[];
}

interface AddCustomSectionProps {
  sections: Section[];
  onSectionsChange: (sections: Section[]) => void;
}

export const AddCustomSection = ({ sections, onSectionsChange }: AddCustomSectionProps) => {
  const [showAddSection, setShowAddSection] = useState(false);
  const [newSection, setNewSection] = useState({
    name: '',
    contentType: 'TEXT_AREA' as const,
  });
  const { toast } = useToast();

  const addCustomSection = () => {
    if (!newSection.name.trim()) return;

    const customSectionData: Section = {
      id: Date.now().toString(),
      type: 'CUSTOM',
      name: newSection.name,
      contentType: newSection.contentType,
      isLocked: false,
      content: [],
    };

    onSectionsChange([...sections, customSectionData]);
    setNewSection({ name: '', contentType: 'TEXT_AREA' });
    setShowAddSection(false);
    
    toast({
      title: "Custom section added!",
      description: `${newSection.name} section has been added to your profile.`,
    });
  };

  return (
    <div>
      <Label className="text-sm font-medium">Add Custom Section</Label>
      {!showAddSection ? (
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowAddSection(true)}
          className="gap-2 mt-2"
        >
          <Plus className="w-4 h-4" />
          Add Custom Section
        </Button>
      ) : (
        <div className="mt-2 p-4 border rounded-lg space-y-3">
          <div>
            <Label htmlFor="section-name">Section Name</Label>
            <Input
              id="section-name"
              value={newSection.name}
              onChange={(e) => setNewSection({ ...newSection, name: e.target.value })}
              placeholder="Enter section name"
            />
          </div>
          <div>
            <Label htmlFor="content-type">Content Type</Label>
            <Select
              value={newSection.contentType}
              onValueChange={(value: any) => setNewSection({ ...newSection, contentType: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="TEXT_AREA">Text Area</SelectItem>
                <SelectItem value="LIST_OF_ITEMS">List of Items</SelectItem>
                <SelectItem value="SOCIAL_MEDIA_LINKS">Social Media Links</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <Button onClick={addCustomSection} size="sm">Add Section</Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowAddSection(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
