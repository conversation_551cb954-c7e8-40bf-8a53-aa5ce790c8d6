
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { MessageCircle, UserX, Send } from 'lucide-react';

interface FeedbackEntry {
  id: string;
  recipientName: string;
  recipientEmail: string;
  feedback: string;
  reason: string;
  sentAt: string;
  isAccessRemoved: boolean;
}

interface FeedbackSystemProps {
  feedbacks: FeedbackEntry[];
  onFeedbacksChange: (feedbacks: FeedbackEntry[]) => void;
}

export const FeedbackSystem = ({ feedbacks, onFeedbacksChange }: FeedbackSystemProps) => {
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [newFeedback, setNewFeedback] = useState({
    recipientName: '',
    recipientEmail: '',
    feedback: '',
    reason: '',
  });
  const { toast } = useToast();

  const sendFeedback = () => {
    if (!newFeedback.recipientName || !newFeedback.recipientEmail || !newFeedback.feedback) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    const feedback: FeedbackEntry = {
      id: Date.now().toString(),
      ...newFeedback,
      sentAt: new Date().toISOString(),
      isAccessRemoved: true,
    };

    onFeedbacksChange([...feedbacks, feedback]);
    setNewFeedback({
      recipientName: '',
      recipientEmail: '',
      feedback: '',
      reason: '',
    });
    setShowFeedbackForm(false);
    
    toast({
      title: "Feedback sent!",
      description: `Feedback has been sent to ${newFeedback.recipientName} and mutual access has been removed.`,
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Dating Feedback System
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!showFeedbackForm ? (
            <div className="space-y-4">
              <p className="text-gray-600">
                When you decide to stop dating someone, you can send them personal feedback 
                and automatically remove mutual access to your profiles.
              </p>
              <Button onClick={() => setShowFeedbackForm(true)} className="gap-2">
                <UserX className="w-4 h-4" />
                End Connection & Send Feedback
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="recipient-name">Recipient Name *</Label>
                <Input
                  id="recipient-name"
                  placeholder="Enter their name"
                  value={newFeedback.recipientName}
                  onChange={(e) => setNewFeedback({ ...newFeedback, recipientName: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="recipient-email">Recipient Email *</Label>
                <Input
                  id="recipient-email"
                  type="email"
                  placeholder="Enter their email"
                  value={newFeedback.recipientEmail}
                  onChange={(e) => setNewFeedback({ ...newFeedback, recipientEmail: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="reason">Reason for Ending Connection</Label>
                <Input
                  id="reason"
                  placeholder="e.g., Different life goals, lack of compatibility..."
                  value={newFeedback.reason}
                  onChange={(e) => setNewFeedback({ ...newFeedback, reason: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="feedback-message">Personal Feedback *</Label>
                <Textarea
                  id="feedback-message"
                  placeholder="Write your honest but respectful feedback here..."
                  rows={4}
                  value={newFeedback.feedback}
                  onChange={(e) => setNewFeedback({ ...newFeedback, feedback: e.target.value })}
                />
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <p className="text-sm text-yellow-800">
                  <strong>Note:</strong> Sending this feedback will automatically remove mutual access 
                  to both of your profiles. This action cannot be undone.
                </p>
              </div>
              <div className="flex gap-2">
                <Button onClick={sendFeedback} className="gap-2">
                  <Send className="w-4 h-4" />
                  Send Feedback & Remove Access
                </Button>
                <Button variant="outline" onClick={() => setShowFeedbackForm(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sent Feedback History */}
      {feedbacks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Feedback History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {feedbacks.map((feedback) => (
                <div key={feedback.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">{feedback.recipientName}</h4>
                      <p className="text-sm text-gray-500">{feedback.recipientEmail}</p>
                    </div>
                    <span className="text-xs text-gray-400">
                      {new Date(feedback.sentAt).toLocaleDateString()}
                    </span>
                  </div>
                  {feedback.reason && (
                    <p className="text-sm mb-2">
                      <strong>Reason:</strong> {feedback.reason}
                    </p>
                  )}
                  <p className="text-sm bg-gray-50 p-2 rounded">
                    <strong>Feedback:</strong> {feedback.feedback}
                  </p>
                  <div className="mt-2 flex items-center gap-2">
                    <UserX className="h-4 w-4 text-red-500" />
                    <span className="text-sm text-red-600">Access removed</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
