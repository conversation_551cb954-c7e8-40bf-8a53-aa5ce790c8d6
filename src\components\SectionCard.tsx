
import { Card, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { X, Lock } from 'lucide-react';
import { SectionContent } from './SectionContent';

interface Section {
  id: string;
  type: 'PREDEFINED' | 'CUSTOM';
  name: string;
  contentType: 'TEXT_AREA' | 'LIST_OF_ITEMS' | 'COMPLEX_OBJECT_LIST' | 'SOCIAL_MEDIA_LINKS';
  isLocked: boolean;
  password?: string;
  content: any[];
}

interface SectionCardProps {
  section: Section;
  onToggleLock: (sectionId: string) => void;
  onDelete: (sectionId: string) => void;
}

export const SectionCard = ({ section, onToggleLock, onDelete }: SectionCardProps) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="text-lg">{section.name}</CardTitle>
            <Badge variant={section.type === 'PREDEFINED' ? 'default' : 'secondary'}>
              {section.type === 'PREDEFINED' ? 'Standard' : 'Custom'}
            </Badge>
            {section.isLocked && (
              <Badge variant="outline" className="gap-1">
                <Lock className="w-3 h-3" />
                Locked
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <Label htmlFor={`lock-${section.id}`} className="text-sm">
                Private
              </Label>
              <Switch
                id={`lock-${section.id}`}
                checked={section.isLocked}
                onCheckedChange={() => onToggleLock(section.id)}
              />
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(section.id)}
              className="text-red-600 hover:text-red-700"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <SectionContent section={section} />
        
        {section.isLocked && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <Label className="text-sm font-medium">Section Password</Label>
            <Input
              type="password"
              placeholder="Set password for this section"
              className="mt-1"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};
