
import { Users } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

interface VisibilitySettings {
  isProfileVisible: boolean;
  hideFromPublic: boolean;
  relationshipStatus: 'single' | 'dating' | 'married';
  partnerEmail?: string;
  hideFromEveryone: boolean;
}

interface HideFromPublicToggleProps {
  settings: VisibilitySettings;
  onUpdate: (key: keyof VisibilitySettings, value: any) => void;
}

export const HideFromPublicToggle = ({ settings, onUpdate }: HideFromPublicToggleProps) => {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-orange-500" />
          <Label className="font-medium">Hide from Public (Non-Users)</Label>
        </div>
        <p className="text-sm text-gray-600">
          Only registered users can view your profile
        </p>
      </div>
      <Switch
        checked={settings.hideFromPublic}
        onCheckedChange={(checked) => onUpdate('hideFromPublic', checked)}
        disabled={settings.hideFromEveryone}
      />
    </div>
  );
};
