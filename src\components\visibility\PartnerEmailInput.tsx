
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

interface VisibilitySettings {
  isProfileVisible: boolean;
  hideFromPublic: boolean;
  relationshipStatus: 'single' | 'dating' | 'married';
  partnerEmail?: string;
  hideFromEveryone: boolean;
}

interface PartnerEmailInputProps {
  settings: VisibilitySettings;
  onUpdate: (key: keyof VisibilitySettings, value: any) => void;
}

export const PartnerEmailInput = ({ settings, onUpdate }: PartnerEmailInputProps) => {
  const [partnerEmail, setPartnerEmail] = useState(settings.partnerEmail || '');
  const { toast } = useToast();

  const savePartnerEmail = () => {
    if (settings.relationshipStatus !== 'single' && partnerEmail.trim()) {
      onUpdate('partnerEmail', partnerEmail.trim());
      toast({
        title: "Partner email saved",
        description: "Your relationship status has been updated.",
      });
    }
  };

  if (settings.relationshipStatus === 'single') {
    return null;
  }

  return (
    <div className="space-y-3">
      <Label htmlFor="partner-email">
        {settings.relationshipStatus === 'dating' ? 'Partner Email' : 'Spouse Email'}
      </Label>
      <div className="flex gap-2">
        <Input
          id="partner-email"
          type="email"
          placeholder={`Enter your ${settings.relationshipStatus === 'dating' ? 'partner' : 'spouse'}'s email`}
          value={partnerEmail}
          onChange={(e) => setPartnerEmail(e.target.value)}
        />
        <Button onClick={savePartnerEmail} disabled={!partnerEmail.trim()}>
          Save
        </Button>
      </div>
      <p className="text-sm text-gray-600">
        Only you and this person will be able to see each other's profiles.
      </p>
    </div>
  );
};
