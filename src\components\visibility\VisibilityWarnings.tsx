
interface VisibilitySettings {
  isProfileVisible: boolean;
  hideFromPublic: boolean;
  relationshipStatus: 'single' | 'dating' | 'married';
  partnerEmail?: string;
  hideFromEveryone: boolean;
}

interface VisibilityWarningsProps {
  settings: VisibilitySettings;
}

export const VisibilityWarnings = ({ settings }: VisibilityWarningsProps) => {
  return (
    <div className="space-y-4">
      {!settings.isProfileVisible && (
        <div className="p-4 bg-red-50 rounded-lg border border-red-200">
          <p className="text-sm text-red-800">
            <strong>Warning:</strong> Your profile is currently disabled and not visible to anyone.
          </p>
        </div>
      )}

      {settings.relationshipStatus !== 'single' && !settings.partnerEmail && (
        <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> Please add your {settings.relationshipStatus === 'dating' ? 'partner' : 'spouse'}'s email to enable restricted visibility.
          </p>
        </div>
      )}
    </div>
  );
};
