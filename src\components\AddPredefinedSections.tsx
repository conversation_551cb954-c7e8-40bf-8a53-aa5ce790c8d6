
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Plus } from 'lucide-react';

interface Section {
  id: string;
  type: 'PREDEFINED' | 'CUSTOM';
  name: string;
  contentType: 'TEXT_AREA' | 'LIST_OF_ITEMS' | 'COMPLEX_OBJECT_LIST' | 'SOCIAL_MEDIA_LINKS';
  isLocked: boolean;
  password?: string;
  content: any[];
}

interface PredefinedSection {
  name: string;
  contentType: 'TEXT_AREA' | 'LIST_OF_ITEMS' | 'COMPLEX_OBJECT_LIST' | 'SOCIAL_MEDIA_LINKS';
}

interface AddPredefinedSectionsProps {
  sections: Section[];
  onSectionsChange: (sections: Section[]) => void;
}

const PREDEFINED_SECTIONS: PredefinedSection[] = [
  { name: 'Interests', contentType: 'LIST_OF_ITEMS' },
  { name: 'Education', contentType: 'COMPLEX_OBJECT_LIST' },
  { name: 'Work Experience', contentType: 'COMPLEX_OBJECT_LIST' },
  { name: 'Family Background', contentType: 'TEXT_AREA' },
];

export const AddPredefinedSections = ({ sections, onSectionsChange }: AddPredefinedSectionsProps) => {
  const { toast } = useToast();

  const addPredefinedSection = (predefined: PredefinedSection) => {
    const newSectionData: Section = {
      id: Date.now().toString(),
      type: 'PREDEFINED',
      name: predefined.name,
      contentType: predefined.contentType,
      isLocked: false,
      content: [],
    };

    onSectionsChange([...sections, newSectionData]);
    toast({
      title: "Section added!",
      description: `${predefined.name} section has been added to your profile.`,
    });
  };

  const availablePredefined = PREDEFINED_SECTIONS.filter(
    predefined => !sections.some(section => section.name === predefined.name)
  );

  if (availablePredefined.length === 0) {
    return null;
  }

  return (
    <div>
      <Label className="text-sm font-medium">Add Standard Sections</Label>
      <div className="flex flex-wrap gap-2 mt-2">
        {availablePredefined.map((predefined) => (
          <Button
            key={predefined.name}
            variant="outline"
            size="sm"
            onClick={() => addPredefinedSection(predefined)}
            className="gap-2"
          >
            <Plus className="w-4 h-4" />
            {predefined.name}
          </Button>
        ))}
      </div>
    </div>
  );
};
