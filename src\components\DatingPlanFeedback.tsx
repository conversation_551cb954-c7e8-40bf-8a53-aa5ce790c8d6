
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Heart, Clock, CheckCircle, XCircle, Send } from 'lucide-react';

interface DateRequest {
  id: string;
  fromUserId: string;
  fromUserName: string;
  toUserId: string;
  planTitle: string;
  planDescription: string;
  proposedDate: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
}

interface DateFeedback {
  id: string;
  requestId: string;
  fromUserId: string;
  toUserId: string;
  rating: number;
  comment: string;
  createdAt: string;
}

interface DatingPlanFeedbackProps {
  feedbacks: DateFeedback[];
  requests: DateRequest[];
  onFeedbacksChange: (feedbacks: DateFeedback[]) => void;
  onRequestsChange: (requests: DateRequest[]) => void;
}

export const DatingPlanFeedback = ({ 
  feedbacks, 
  requests, 
  onFeedbacksChange, 
  onRequestsChange 
}: DatingPlanFeedbackProps) => {
  const [newRequest, setNewRequest] = useState({
    toUserEmail: '',
    planTitle: '',
    planDescription: '',
    proposedDate: ''
  });
  const [newFeedback, setNewFeedback] = useState({
    requestId: '',
    rating: 5,
    comment: ''
  });
  const { toast } = useToast();

  const sendDateRequest = () => {
    if (!newRequest.toUserEmail || !newRequest.planTitle || !newRequest.proposedDate) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    const request: DateRequest = {
      id: Date.now().toString(),
      fromUserId: 'current-user-id',
      fromUserName: 'Current User',
      toUserId: 'target-user-id',
      planTitle: newRequest.planTitle,
      planDescription: newRequest.planDescription,
      proposedDate: newRequest.proposedDate,
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    onRequestsChange([...requests, request]);
    setNewRequest({ toUserEmail: '', planTitle: '', planDescription: '', proposedDate: '' });
    
    toast({
      title: "Date request sent!",
      description: "Your dating plan request has been sent.",
    });
  };

  const respondToRequest = (requestId: string, status: 'accepted' | 'rejected') => {
    const updatedRequests = requests.map(request => 
      request.id === requestId ? { ...request, status } : request
    );
    onRequestsChange(updatedRequests);
    
    toast({
      title: status === 'accepted' ? "Request accepted!" : "Request declined",
      description: `You have ${status} the dating plan request.`,
    });
  };

  const submitFeedback = () => {
    if (!newFeedback.requestId || !newFeedback.comment) {
      toast({
        title: "Missing information",
        description: "Please select a request and add a comment.",
        variant: "destructive"
      });
      return;
    }

    const feedback: DateFeedback = {
      id: Date.now().toString(),
      requestId: newFeedback.requestId,
      fromUserId: 'current-user-id',
      toUserId: 'target-user-id',
      rating: newFeedback.rating,
      comment: newFeedback.comment,
      createdAt: new Date().toISOString()
    };

    onFeedbacksChange([...feedbacks, feedback]);
    setNewFeedback({ requestId: '', rating: 5, comment: '' });
    
    toast({
      title: "Feedback submitted!",
      description: "Thank you for your feedback on the date.",
    });
  };

  return (
    <div className="space-y-6">
      {/* Send Date Request */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5" />
            Send Date Request
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="to-user">To User (Email)</Label>
            <Input
              id="to-user"
              type="email"
              placeholder="Enter user's email"
              value={newRequest.toUserEmail}
              onChange={(e) => setNewRequest(prev => ({ ...prev, toUserEmail: e.target.value }))}
            />
          </div>
          <div>
            <Label htmlFor="plan-title">Date Plan Title</Label>
            <Input
              id="plan-title"
              placeholder="e.g., Coffee Date at Central Park"
              value={newRequest.planTitle}
              onChange={(e) => setNewRequest(prev => ({ ...prev, planTitle: e.target.value }))}
            />
          </div>
          <div>
            <Label htmlFor="plan-description">Description</Label>
            <Textarea
              id="plan-description"
              placeholder="Describe your date plan..."
              value={newRequest.planDescription}
              onChange={(e) => setNewRequest(prev => ({ ...prev, planDescription: e.target.value }))}
            />
          </div>
          <div>
            <Label htmlFor="proposed-date">Proposed Date</Label>
            <Input
              id="proposed-date"
              type="datetime-local"
              value={newRequest.proposedDate}
              onChange={(e) => setNewRequest(prev => ({ ...prev, proposedDate: e.target.value }))}
            />
          </div>
          <Button onClick={sendDateRequest} className="gap-2">
            <Send className="w-4 h-4" />
            Send Request
          </Button>
        </CardContent>
      </Card>

      {/* Incoming Requests */}
      <Card>
        <CardHeader>
          <CardTitle>Incoming Date Requests</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {requests.filter(req => req.status === 'pending').length === 0 ? (
            <p className="text-gray-500 text-center py-4">No pending requests</p>
          ) : (
            requests.filter(req => req.status === 'pending').map((request) => (
              <div key={request.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div>
                    <h4 className="font-medium">{request.planTitle}</h4>
                    <p className="text-sm text-gray-600">From: {request.fromUserName}</p>
                    <p className="text-sm text-gray-500">{request.planDescription}</p>
                    <p className="text-sm text-blue-600">
                      Proposed: {new Date(request.proposedDate).toLocaleString()}
                    </p>
                  </div>
                  <Badge variant="outline" className="gap-1">
                    <Clock className="w-3 h-3" />
                    Pending
                  </Badge>
                </div>
                <div className="flex gap-2">
                  <Button 
                    size="sm" 
                    onClick={() => respondToRequest(request.id, 'accepted')}
                    className="gap-1"
                  >
                    <CheckCircle className="w-4 h-4" />
                    Accept
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => respondToRequest(request.id, 'rejected')}
                    className="gap-1"
                  >
                    <XCircle className="w-4 h-4" />
                    Decline
                  </Button>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Request History */}
      <Card>
        <CardHeader>
          <CardTitle>Request History</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {requests.filter(req => req.status !== 'pending').length === 0 ? (
            <p className="text-gray-500 text-center py-4">No completed requests</p>
          ) : (
            requests.filter(req => req.status !== 'pending').map((request) => (
              <div key={request.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h4 className="font-medium">{request.planTitle}</h4>
                    <p className="text-sm text-gray-600">With: {request.fromUserName}</p>
                    <p className="text-sm text-gray-500">
                      Date: {new Date(request.proposedDate).toLocaleString()}
                    </p>
                  </div>
                  <Badge 
                    variant={request.status === 'accepted' ? 'default' : 'secondary'}
                    className="gap-1"
                  >
                    {request.status === 'accepted' ? (
                      <CheckCircle className="w-3 h-3" />
                    ) : (
                      <XCircle className="w-3 h-3" />
                    )}
                    {request.status}
                  </Badge>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Leave Feedback */}
      <Card>
        <CardHeader>
          <CardTitle>Leave Date Feedback</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="feedback-request">Select Completed Date</Label>
            <select
              id="feedback-request"
              className="w-full p-2 border rounded-md"
              value={newFeedback.requestId}
              onChange={(e) => setNewFeedback(prev => ({ ...prev, requestId: e.target.value }))}
            >
              <option value="">Select a completed date...</option>
              {requests.filter(req => req.status === 'accepted').map(request => (
                <option key={request.id} value={request.id}>
                  {request.planTitle} - {new Date(request.proposedDate).toLocaleDateString()}
                </option>
              ))}
            </select>
          </div>
          <div>
            <Label htmlFor="rating">Rating (1-5)</Label>
            <Input
              id="rating"
              type="number"
              min="1"
              max="5"
              value={newFeedback.rating}
              onChange={(e) => setNewFeedback(prev => ({ ...prev, rating: parseInt(e.target.value) }))}
            />
          </div>
          <div>
            <Label htmlFor="feedback-comment">Comment</Label>
            <Textarea
              id="feedback-comment"
              placeholder="How was your date experience?"
              value={newFeedback.comment}
              onChange={(e) => setNewFeedback(prev => ({ ...prev, comment: e.target.value }))}
            />
          </div>
          <Button onClick={submitFeedback}>Submit Feedback</Button>
        </CardContent>
      </Card>

      {/* Feedback History */}
      <Card>
        <CardHeader>
          <CardTitle>Feedback History</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {feedbacks.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No feedback submitted yet</p>
          ) : (
            feedbacks.map((feedback) => (
              <div key={feedback.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Rating: {feedback.rating}/5</span>
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Heart
                            key={i}
                            className={`w-4 h-4 ${
                              i < feedback.rating ? 'text-red-500 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{feedback.comment}</p>
                  </div>
                  <span className="text-xs text-gray-500">
                    {new Date(feedback.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  );
};
