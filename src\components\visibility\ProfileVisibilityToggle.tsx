
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

interface VisibilitySettings {
  isProfileVisible: boolean;
  hideFromPublic: boolean;
  relationshipStatus: 'single' | 'dating' | 'married';
  partnerEmail?: string;
  hideFromEveryone: boolean;
}

interface ProfileVisibilityToggleProps {
  settings: VisibilitySettings;
  onUpdate: (key: keyof VisibilitySettings, value: any) => void;
}

export const ProfileVisibilityToggle = ({ settings, onUpdate }: ProfileVisibilityToggleProps) => {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="space-y-1">
        <Label className="font-medium">Profile Visible</Label>
        <p className="text-sm text-gray-600">
          Master switch for profile visibility (overrides all other settings)
        </p>
      </div>
      <Switch
        checked={settings.isProfileVisible}
        onCheckedChange={(checked) => onUpdate('isProfileVisible', checked)}
      />
    </div>
  );
};
