
import { Heart } from 'lucide-react';
import { Label } from '@/components/ui/label';

interface VisibilitySettings {
  isProfileVisible: boolean;
  hideFromPublic: boolean;
  relationshipStatus: 'single' | 'dating' | 'married';
  partnerEmail?: string;
  hideFromEveryone: boolean;
}

interface RelationshipStatusSelectorProps {
  settings: VisibilitySettings;
  onUpdate: (key: keyof VisibilitySettings, value: any) => void;
}

export const RelationshipStatusSelector = ({ settings, onUpdate }: RelationshipStatusSelectorProps) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Heart className="h-4 w-4 text-pink-500" />
        <Label className="font-medium">Relationship Status</Label>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {(['single', 'dating', 'married'] as const).map((status) => (
          <div
            key={status}
            className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
              settings.relationshipStatus === status
                ? 'border-primary bg-primary/5'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => onUpdate('relationshipStatus', status)}
          >
            <div className="text-center">
              <div className="font-medium capitalize">{status}</div>
              <div className="text-xs text-gray-500 mt-1">
                {status === 'single' && 'Visible to everyone'}
                {status === 'dating' && 'Only visible to partner'}
                {status === 'married' && 'Only visible to spouse'}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
