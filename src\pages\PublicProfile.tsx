
import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Heart, Lock, Unlock, User } from 'lucide-react';

const PublicProfile = () => {
  const { userId } = useParams();
  const [profile, setProfile] = useState<any>(null);
  const [unlockedSections, setUnlockedSections] = useState<Set<string>>(new Set());
  const [unlockPasswords, setUnlockPasswords] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // Mock API call to fetch public profile
    const fetchProfile = async () => {
      console.log('Fetching public profile for user:', userId);
      
      // Mock profile data
      const mockProfile = {
        id: userId,
        name: 'Sarah Johnson',
        aboutMe: 'A passionate educator and traveler who loves meeting new people and experiencing different cultures. I believe in the importance of family values and building meaningful relationships.',
        profilePictureUrl: '',
        sections: [
          {
            id: '1',
            name: 'Interests',
            contentType: 'LIST_OF_ITEMS',
            isLocked: false,
            content: ['Reading', 'Photography', 'Cooking', 'Hiking', 'Yoga']
          },
          {
            id: '2',
            name: 'Education',
            contentType: 'COMPLEX_OBJECT_LIST',
            isLocked: false,
            content: [
              { institution: 'University of California', degree: 'Masters in Education', graduationYear: '2020' },
              { institution: 'Stanford University', degree: 'Bachelor of Arts', graduationYear: '2018' }
            ]
          },
          {
            id: '3',
            name: 'Family Background',
            contentType: 'TEXT_AREA',
            isLocked: true,
            content: 'This section contains private family information.'
          },
          {
            id: '4',
            name: 'Personal Values',
            contentType: 'TEXT_AREA',
            isLocked: true,
            content: 'My personal values and beliefs that guide my life decisions.'
          }
        ]
      };

      setProfile(mockProfile);
      setIsLoading(false);
    };

    fetchProfile();
  }, [userId]);

  const handleUnlockSection = (sectionId: string) => {
    const password = unlockPasswords[sectionId];
    
    // Mock password validation (in real app, this would be an API call)
    if (password === 'demo') {
      setUnlockedSections(prev => new Set([...prev, sectionId]));
      toast({
        title: "Section unlocked!",
        description: "You can now view this private section.",
      });
    } else {
      toast({
        title: "Incorrect password",
        description: "Please enter the correct password to unlock this section.",
        variant: "destructive",
      });
    }
  };

  const renderSectionContent = (section: any) => {
    if (section.contentType === 'LIST_OF_ITEMS') {
      return (
        <div className="flex flex-wrap gap-2">
          {section.content.map((item: string, index: number) => (
            <Badge key={index} variant="secondary">{item}</Badge>
          ))}
        </div>
      );
    }
    
    if (section.contentType === 'COMPLEX_OBJECT_LIST') {
      return (
        <div className="space-y-3">
          {section.content.map((item: any, index: number) => (
            <div key={index} className="p-3 bg-gray-50 rounded-lg">
              {section.name === 'Education' && (
                <div>
                  <p className="font-medium">{item.degree}</p>
                  <p className="text-sm text-gray-600">{item.institution}</p>
                  <p className="text-sm text-gray-500">Graduated: {item.graduationYear}</p>
                </div>
              )}
              {section.name === 'Work Experience' && (
                <div>
                  <p className="font-medium">{item.role}</p>
                  <p className="text-sm text-gray-600">{item.company}</p>
                  <p className="text-sm text-gray-500">{item.startDate} - {item.endDate}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      );
    }
    
    if (section.contentType === 'TEXT_AREA') {
      return <p className="text-gray-700 leading-relaxed">{section.content}</p>;
    }

    return null;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Profile not found</h1>
          <p className="text-gray-600">The profile you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Heart className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-xl font-bold">MarriageBiograph</h1>
              <p className="text-sm text-gray-600">Public Profile</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Profile Header */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                <Avatar className="w-32 h-32">
                  <AvatarImage src={profile.profilePictureUrl} />
                  <AvatarFallback className="text-2xl">
                    <User className="w-16 h-16" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 text-center md:text-left">
                  <h1 className="text-3xl font-bold mb-4">{profile.name}</h1>
                  <p className="text-gray-700 leading-relaxed">{profile.aboutMe}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Profile Sections */}
          <div className="space-y-6">
            {profile.sections.map((section: any) => (
              <Card key={section.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {section.name}
                      {section.isLocked && (
                        <Lock className="w-5 h-5 text-gray-500" />
                      )}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  {!section.isLocked || unlockedSections.has(section.id) ? (
                    <div className="animate-fade-in">
                      {renderSectionContent(section)}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Lock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">This section is locked. Enter password to view.</p>
                      <div className="max-w-sm mx-auto flex gap-2">
                        <Input
                          type="password"
                          placeholder="Enter password"
                          value={unlockPasswords[section.id] || ''}
                          onChange={(e) => setUnlockPasswords({
                            ...unlockPasswords,
                            [section.id]: e.target.value
                          })}
                        />
                        <Button 
                          onClick={() => handleUnlockSection(section.id)}
                          className="gap-2"
                        >
                          <Unlock className="w-4 h-4" />
                          Unlock
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Hint: Use "demo" as password for this demo
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicProfile;
