
import { Eye, EyeOff } from 'lucide-react';

interface VisibilitySettings {
  isProfileVisible: boolean;
  hideFromPublic: boolean;
  relationshipStatus: 'single' | 'dating' | 'married';
  partnerEmail?: string;
  hideFromEveryone: boolean;
}

interface VisibilityStatusCardProps {
  settings: VisibilitySettings;
}

export const VisibilityStatusCard = ({ settings }: VisibilityStatusCardProps) => {
  const getVisibilityDescription = () => {
    if (settings.hideFromEveryone) {
      return "Your profile is completely hidden from everyone.";
    }
    if (settings.hideFromPublic) {
      return "Your profile is only visible to registered users.";
    }
    if (settings.relationshipStatus === 'dating' || settings.relationshipStatus === 'married') {
      return `Your profile is only visible to ${settings.partnerEmail || 'your partner'} and hidden from others.`;
    }
    return "Your profile is visible to everyone.";
  };

  return (
    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
      <div className="flex items-center gap-2 mb-2">
        {settings.hideFromEveryone ? (
          <EyeOff className="h-5 w-5 text-red-500" />
        ) : (
          <Eye className="h-5 w-5 text-blue-500" />
        )}
        <span className="font-medium">Current Status</span>
      </div>
      <p className="text-sm text-gray-700">{getVisibilityDescription()}</p>
    </div>
  );
};
