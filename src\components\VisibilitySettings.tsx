
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye } from 'lucide-react';
import { VisibilityStatusCard } from './visibility/VisibilityStatusCard';
import { HideFromEveryoneToggle } from './visibility/HideFromEveryoneToggle';
import { HideFromPublicToggle } from './visibility/HideFromPublicToggle';
import { RelationshipStatusSelector } from './visibility/RelationshipStatusSelector';
import { PartnerEmailInput } from './visibility/PartnerEmailInput';
import { ProfileVisibilityToggle } from './visibility/ProfileVisibilityToggle';
import { VisibilityWarnings } from './visibility/VisibilityWarnings';

interface VisibilitySettings {
  isProfileVisible: boolean;
  hideFromPublic: boolean;
  relationshipStatus: 'single' | 'dating' | 'married';
  partnerEmail?: string;
  hideFromEveryone: boolean;
}

interface VisibilitySettingsProps {
  settings: VisibilitySettings;
  onSettingsChange: (settings: VisibilitySettings) => void;
}

export const VisibilitySettings = ({ settings, onSettingsChange }: VisibilitySettingsProps) => {
  const updateSetting = (key: keyof VisibilitySettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    onSettingsChange(newSettings);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Profile Visibility Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <VisibilityStatusCard settings={settings} />
          <HideFromEveryoneToggle settings={settings} onUpdate={updateSetting} />
          <HideFromPublicToggle settings={settings} onUpdate={updateSetting} />
          <RelationshipStatusSelector settings={settings} onUpdate={updateSetting} />
          <PartnerEmailInput settings={settings} onUpdate={updateSetting} />
          <ProfileVisibilityToggle settings={settings} onUpdate={updateSetting} />
          <VisibilityWarnings settings={settings} />
        </CardContent>
      </Card>
    </div>
  );
};
