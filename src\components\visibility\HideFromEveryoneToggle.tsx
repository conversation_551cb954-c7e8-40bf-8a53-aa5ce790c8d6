
import { Lock } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

interface VisibilitySettings {
  isProfileVisible: boolean;
  hideFromPublic: boolean;
  relationshipStatus: 'single' | 'dating' | 'married';
  partnerEmail?: string;
  hideFromEveryone: boolean;
}

interface HideFromEveryoneToggleProps {
  settings: VisibilitySettings;
  onUpdate: (key: keyof VisibilitySettings, value: any) => void;
}

export const HideFromEveryoneToggle = ({ settings, onUpdate }: HideFromEveryoneToggleProps) => {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <Lock className="h-4 w-4 text-red-500" />
          <Label className="font-medium">Hide Account Completely</Label>
        </div>
        <p className="text-sm text-gray-600">
          Make your profile invisible to everyone, including registered users
        </p>
      </div>
      <Switch
        checked={settings.hideFromEveryone}
        onCheckedChange={(checked) => onUpdate('hideFromEveryone', checked)}
      />
    </div>
  );
};
